<!DOCTYPE html>
<html lang="zh">
<head>
<meta charset="UTF-8" />
<title>OKX K线回放（稳定版 · 加载即显示 · 播放后保留）</title>
<script src="https://cdn.jsdelivr.net/npm/lightweight-charts@4.1.1/dist/lightweight-charts.standalone.production.js"></script>
<style>
  html,body{margin:0;padding:0;background:#000;color:#fff;font-family:system-ui,-apple-system,"Segoe UI",Roboto,Arial}
  #chartMainWrap{width:100vw;height:56vh;border-bottom:1px solid #111}
  #mainResizer{width:100vw;height:8px;background:#111;cursor:ns-resize}
  #panes{width:100vw}
  .pane-block{position:relative;width:100%;height:14vh;min-height:90px;border-top:1px solid #111;user-select:none}
  .pane-header{position:absolute;left:0;right:0;top:0;height:28px;display:flex;gap:8px;align-items:center;padding:0 8px;background:#0b0b0b;border-bottom:1px solid #111;color:#9aa;font-size:12px}
  .pane-header .title{flex:1}
  .pane-header button{background:#222;color:#ddd;border:1px solid #444;border-radius:4px;padding:2px 6px;cursor:pointer}
  .pane-header button:hover{background:#333}
  .pane-body{position:absolute;left:0;right:0;top:28px;bottom:6px}
  .resizer{position:absolute;left:0;right:0;bottom:0;height:6px;background:#111;cursor:ns-resize}
  #controls{padding:8px 12px;display:flex;flex-wrap:wrap;gap:12px;align-items:center;border-top:1px solid #222;background:#000}
  select,input[type="number"],input[type="range"],input[type="datetime-local"],button,label{background:#222;color:#fff;border:1px solid #444;border-radius:6px;padding:6px 10px;font-size:14px}
  button{cursor:pointer}button:hover{background:#333}
  .inline{display:flex;gap:8px;align-items:center}
  .muted{opacity:.75;font-size:13px}
  #ctxMenu{position:fixed;z-index:1000;background:#111;border:1px solid #444;border-radius:6px;display:none;min-width:160px}
  #ctxMenu button{display:block;width:100%;text-align:left;border:0;border-bottom:1px solid #333;border-radius:0;background:#111;padding:8px 10px}
  #ctxMenu button:hover{background:#222}
  #ctxMenu button:last-child{border-bottom:0}
</style>
</head>
<body>

<!-- 主图 -->
<div id="chartMainWrap"></div>
<div id="mainResizer" title="拖动调整主图高度"></div>

<!-- 三个副图 -->
<div id="panes">
  <div class="pane-block" id="paneVOL">
    <div class="pane-header">
      <span class="title">VOL 成交量</span>
      <button class="btn-toggle">收起</button><button class="btn-up">▲ 上移</button><button class="btn-down">▼ 下移</button>
    </div>
    <div class="pane-body" id="paneVOLBody"></div>
    <div class="resizer"></div>
  </div>
  <div class="pane-block" id="paneMACD">
    <div class="pane-header">
      <span class="title">MACD (12,26,9)</span>
      <button class="btn-toggle">收起</button><button class="btn-up">▲ 上移</button><button class="btn-down">▼ 下移</button>
    </div>
    <div class="pane-body" id="paneMACDBody"></div>
    <div class="resizer"></div>
  </div>
  <div class="pane-block" id="paneKDJ">
    <div class="pane-header">
      <span class="title">KDJ (9,3,3)</span>
      <button class="btn-toggle">收起</button><button class="btn-up">▲ 上移</button><button class="btn-down">▼ 下移</button>
    </div>
    <div class="pane-body" id="paneKDJBody"></div>
    <div class="resizer"></div>
  </div>
</div>

<!-- 控件区 -->
<div id="controls">
  <label>交易对：
    <select id="symbolSel">
      <option>BTC-USDT-SWAP</option><option>ETH-USDT-SWAP</option><option>SOL-USDT-SWAP</option>
      <option>XRP-USDT-SWAP</option><option>BNB-USDT-SWAP</option><option>DOGE-USDT-SWAP</option><option>LTC-USDT-SWAP</option>
    </select>
  </label>
  <label>周期：
    <select id="barSel">
      <option value="1m">1分</option><option value="5m">5分</option><option value="15m">15分</option><option value="30m">30分</option>
      <option value="1H">1时</option><option value="2H">2时</option><option value="4H">4时</option><option value="6H">6时</option><option value="12H">12时</option>
      <option value="1D">1日</option><option value="2D">2日</option><option value="3D">3日</option><option value="5D">5日</option>
      <option value="1W">1周</option><option value="1M">1月</option><option value="3M">3月</option>
    </select>
  </label>

  <div class="inline">
    <label>速度（分/秒）：</label>
    <input id="speedRange" type="range" min="0.2" max="120" step="0.2" value="1" />
    <input id="speedInput" type="number" min="0.2" max="120" step="0.2" value="1" style="width:90px" />
  </div>

  <div class="inline">
    <label>加载分钟数（0=自动）：<input id="minutesInput" type="number" min="0" step="60" value="1440" style="width:120px"/></label>
  </div>

  <div class="inline">
    <label>自定义开始时间：<input id="jumpTime" type="datetime-local" /></label>
    <button id="jumpBtn">跳转并播放</button>
    <button id="startFromCursorBtn">从光标位置开始</button>
  </div>

  <div class="inline">
    <button id="showPlayedBtn">仅显示播放段</button>
    <button id="btnLoadCache">加载缓存</button>
    <button id="btnSaveCache">保存历史</button>
    <button id="btnFetchNew">拉取新增(20秒内)</button>
    <button id="showAllBtn">显示全部</button>
    <label style="margin-left:12px;">
      <input type="checkbox" id="lockViewChk" checked> 播放时锁定窗口（不自动缩放）
    </label>
  </div>

  <label class="inline" style="gap:6px"><input id="syncToggle" type="checkbox"/> 参与同步（仅播放控制）</label>

  <button id="startBtn">▶ 开始</button>
  <button id="pauseBtn">⏸ 暂停</button>
  <button id="resetBtn">🔄 重置</button>
  <button id="reloadBtn">↻ 重新取数</button>
  <span id="status" class="muted">状态：等待数据…</span>
</div>

<!-- 右键菜单 -->
<div id="ctxMenu">
  <button id="ctxStartHere">从此开始播放</button>
  <button id="ctxShowAll">从此显示全部</button>
</div>

<script>
/* ====== 工具与常量（全局） ====== */
const TF_MINUTES={'1m':1,'5m':5,'15m':15,'30m':30,'1H':60,'2H':120,'4H':240,'6H':360,'12H':720,'1D':1440,'2D':2880,'3D':4320,'5D':7200,'1W':10080,'1M':43200,'3M':129600};
const SMA=(a,p)=>{const o=[],n=a.length;let s=0;for(let i=0;i<n;i++){s+=a[i];if(i>=p)s-=a[i-p];o[i]=i>=p-1?s/p:null}return o};
const EMA=(a,p)=>{const k=2/(p+1),o=[];let e=a[0];o[0]=e;for(let i=1;i<a.length;i++){e=a[i]*k+e*(1-k);o[i]=e}return o};
const STD=(a,p)=>{const o=[];let s=0,ss=0;for(let i=0;i<a.length;i++){s+=a[i];ss+=a[i]*a[i];if(i>=p){s-=a[i-p];ss-=a[i-p]*a[i-p]}o[i]=i>=p-1?Math.sqrt(Math.max(ss/p-(s/p)*(s/p),0)):null}return o};
const HHV=(a,p)=>{const o=[];for(let i=0;i<a.length;i++){if(i<p-1)o.push(null);else{let m=-Infinity;for(let j=i-p+1;j<=i;j++)m=Math.max(m,a[j]);o.push(m)}}return o};
const LLV=(a,p)=>{const o=[];for(let i=0;i<a.length;i++){if(i<p-1)o.push(null);else{let m=Infinity;for(let j=i-p+1;j<=i;j++)m=Math.min(m,a[j]);o.push(m)}}return o};

// 安全设置可视窗：left/right 必须是有限数字且 left<right
function safeSetRange(chart, left, right){
  if(!chart || !chart.timeScale) return;
  const l = Number(left), r = Number(right);
  if(!Number.isFinite(l) || !Number.isFinite(r) || l >= r) return;
  try { chart.timeScale().setVisibleRange({ from:l, to:r }); } catch(_){}
}

// 将秒级时间对齐到当前选择周期的K线起点
function alignTsToFrame(tsSec){
  const tfSec = (TF_MINUTES[selBar.value] || 1) * 60;
  return Math.floor(tsSec / tfSec) * tfSec;
}

/* ====== 主图 & 副图 ====== */
const mainWrap=document.getElementById('chartMainWrap');
const chartMain=LightweightCharts.createChart(mainWrap,{
  layout:{background:{color:'#000'},textColor:'#ddd'},
  grid:{vertLines:{visible:false},horzLines:{visible:false}},
  crosshair:{mode:LightweightCharts.CrosshairMode.Normal},
  timeScale:{timeVisible:true,secondsVisible:false,borderColor:'#444'},
  rightPriceScale:{borderColor:'#444'},
});
const seriesCandle=chartMain.addCandlestickSeries({
  upColor:'green',downColor:'red',wickUpColor:'green',wickDownColor:'red',borderVisible:false
});
const ma5 =chartMain.addLineSeries({color:'#FFD000', lineWidth:1, lastValueVisible:false, priceLineVisible:false});
const ma10=chartMain.addLineSeries({color:'#FF4D4D', lineWidth:1, lastValueVisible:false, priceLineVisible:false});
const ma20=chartMain.addLineSeries({color:'#66CCFF', lineWidth:1, lastValueVisible:false, priceLineVisible:false});
const bollMid=chartMain.addLineSeries({color:'#3399FF', lineWidth:1, lastValueVisible:false, priceLineVisible:false});
const bollUp =chartMain.addLineSeries({color:'#FFD000', lineWidth:1, lineStyle:LightweightCharts.LineStyle.Dashed, lastValueVisible:false, priceLineVisible:false});
const bollDn =chartMain.addLineSeries({color:'#FFD000', lineWidth:1, lineStyle:LightweightCharts.LineStyle.Dashed, lastValueVisible:false, priceLineVisible:false});

function createPaneChart(el){return LightweightCharts.createChart(el,{
  layout:{background:{color:'#000'},textColor:'#ddd'},
  grid:{vertLines:{visible:false},horzLines:{visible:false}},
  crosshair:{mode:LightweightCharts.CrosshairMode.Normal},
  timeScale:{timeVisible:true,secondsVisible:false,borderColor:'#444'},
  rightPriceScale:{borderColor:'#444'},
});}
const chartVOL=createPaneChart(document.getElementById('paneVOLBody'));
const volSeries=chartVOL.addHistogramSeries({priceFormat:{type:'volume'},lastValueVisible:false,priceLineVisible:false});
const chartMACD=createPaneChart(document.getElementById('paneMACDBody'));
const macdHist=chartMACD.addHistogramSeries({base:0,lastValueVisible:false,priceLineVisible:false});
const macdLine=chartMACD.addLineSeries({color:'#00D97E',lineWidth:1,lastValueVisible:false,priceLineVisible:false});
const macdSig =chartMACD.addLineSeries({color:'#FF9F1A',lineWidth:1,lastValueVisible:false,priceLineVisible:false});
const chartKDJ=createPaneChart(document.getElementById('paneKDJBody'));
const kLine=chartKDJ.addLineSeries({color:'#FFD000',lineWidth:1,lastValueVisible:false,priceLineVisible:false});
const dLine=chartKDJ.addLineSeries({color:'#FF4D4D',lineWidth:1,lastValueVisible:false,priceLineVisible:false});
const jLine=chartKDJ.addLineSeries({color:'#2EA0FF',lineWidth:1,lastValueVisible:false,priceLineVisible:false});

// ===== 统一兜底包装：拦截所有 setData，过滤空值/NaN，并打印丢弃数量 =====
(function(){
  if (window.__allSeriesWrapped) return;
  window.__allSeriesWrapped = true;

  function cleanRows(rows, kind){
    rows = Array.isArray(rows) ? rows : [];
    const isCandle = kind === 'candle';
    const ok = [];
    for (const p of rows){
      if (!p || !Number.isFinite(p.time)) continue;
      if (isCandle){
        if ([p.open,p.high,p.low,p.close].every(Number.isFinite)) ok.push(p);
      }else{
        if (Number.isFinite(p.value)) ok.push(p);
      }
    }
    return ok;
  }

  function wrap(series, name, kind){
    if (!series || typeof series.setData !== 'function' || series.__wrapped) return;
    const orig = series.setData.bind(series);
    series.setData = (rows)=>{
      const before = Array.isArray(rows) ? rows.length : 0;
      const clean  = cleanRows(rows, kind);
      const dropped = before - clean.length;
      if (dropped > 0) console.warn(`[filter] ${name}: dropped ${dropped}/${before}`);
      try { orig(clean); } catch(e){ console.error(`[setData error] ${name}`, e); }
    };
    series.__wrapped = true;
  }

  wrap(seriesCandle, 'candle', 'candle');
  wrap(volSeries,    'volume', 'value');
  wrap(ma5,          'ma5',    'value');
  wrap(ma10,         'ma10',   'value');
  wrap(ma20,         'ma20',   'value');
  wrap(bollMid,      'bollMid','value');
  wrap(bollUp,       'bollUp', 'value');
  wrap(bollDn,       'bollDn', 'value');
  wrap(macdLine,     'MACD',   'value');
  wrap(macdSig,      'Signal', 'value');
  wrap(macdHist,     'Hist',   'value');
  wrap(kLine,        'K',      'value');
  wrap(dLine,        'D',      'value');
  wrap(jLine,        'J',      'value');

  console.log('[wrap] all series setData wrapped');
})();

/* ====== 时间轴联动（稳健版） ====== */
function trySetRange(chart, range){
  if(!range || range.from == null || range.to == null) return;
  const from=Number(range.from), to=Number(range.to);
  if(!isFinite(from) || !isFinite(to)) return;
  try{ chart.timeScale().setVisibleRange({from,to}); }catch(_){}
}
let hasDataMain=false, hasDataVol=false, hasDataMacd=false, hasDataKdj=false;
chartMain.timeScale().subscribeVisibleTimeRangeChange((range)=>{
  if(!hasDataMain || !range || range.from == null || range.to == null) return;
  if(hasDataVol)  trySetRange(chartVOL,  range);
  if(hasDataMacd) trySetRange(chartMACD, range);
  if(hasDataKdj)  trySetRange(chartKDJ,  range);
});

/* ====== 自适应与拖拽高度 ====== */
const ro=new ResizeObserver(()=>{
  chartMain.applyOptions({width:mainWrap.clientWidth,height:mainWrap.clientHeight});
  for(const id of ['paneVOL','paneMACD','paneKDJ']){
    const body=document.querySelector('#'+id+' .pane-body');
    const c=id==='paneVOL'?chartVOL:id==='paneMACD'?chartMACD:chartKDJ;
    c.applyOptions({width:body.clientWidth,height:body.clientHeight});
  }
});
ro.observe(document.body);
(()=>{const bar=document.getElementById('mainResizer');let drag=false,sy=0,sh=0;
  bar.addEventListener('mousedown',e=>{drag=true;sy=e.clientY;sh=mainWrap.clientHeight;e.preventDefault();});
  window.addEventListener('mousemove',e=>{if(!drag)return;const nh=Math.max(140,Math.min(window.innerHeight*0.9,sh+(e.clientY-sy)));
    mainWrap.style.height=nh+'px'; ro.takeRecords();});
  window.addEventListener('mouseup',()=>drag=false);
})();
document.querySelectorAll('.pane-block').forEach(block=>{
  const res=block.querySelector('.resizer');let dragging=false,sy=0,sh=0;
  res.addEventListener('mousedown',e=>{dragging=true;sy=e.clientY;sh=block.clientHeight;e.preventDefault();});
  window.addEventListener('mousemove',e=>{if(!dragging)return;block.style.height=Math.max(90,sh+(e.clientY-sy))+'px';ro.takeRecords();});
  window.addEventListener('mouseup',()=>dragging=false);
  block.querySelector('.btn-up').onclick=()=>{const p=block.parentElement,prev=block.previousElementSibling;if(prev)p.insertBefore(block,prev);ro.takeRecords();};
  block.querySelector('.btn-down').onclick=()=>{const p=block.parentElement,next=block.nextElementSibling;if(next)p.insertBefore(next,block);ro.takeRecords();};
  const btnT=block.querySelector('.btn-toggle');btnT.onclick=()=>{
    const body=block.querySelector('.pane-body');
    if(!block.dataset.collapsed){block.dataset.prevH=block.clientHeight;block.style.height='28px';body.style.display='none';block.dataset.collapsed='1';btnT.textContent='展开';}
    else{body.style.display='block';block.style.height=(block.dataset.prevH||140)+'px';delete block.dataset.collapsed;btnT.textContent='收起';ro.takeRecords();}
  };
});

/* ====== 控件引用 ====== */
const selSymbol=document.getElementById('symbolSel');
const selBar=document.getElementById('barSel');
const speedRange=document.getElementById('speedRange');
const speedInput=document.getElementById('speedInput');
const minutesInput=document.getElementById('minutesInput');
const jumpTime=document.getElementById('jumpTime');
const startFromCursorBtn=document.getElementById('startFromCursorBtn');
const statusEl=document.getElementById('status');
const btnStart=document.getElementById('startBtn');
const btnPause=document.getElementById('pauseBtn');
const btnReset=document.getElementById('resetBtn');
const btnReload=document.getElementById('reloadBtn');
const btnShowPlayed=document.getElementById('showPlayedBtn');
const btnShowAll=document.getElementById('showAllBtn');
const syncToggle=document.getElementById('syncToggle');
const lockViewChk = document.getElementById('lockViewChk');
const btnLoadCache = document.getElementById('btnLoadCache');
const btnSaveCache = document.getElementById('btnSaveCache');
const btnFetchNew  = document.getElementById('btnFetchNew');

function setStatus(s){ try{ statusEl.textContent = '状态：' + s; }catch(_){ } }

/* ====== 播放与数据状态 ====== */
let minutesFull=[];         // 完整分钟历史
let idxMin=0;               // 播放进度（分钟索引）
let timer=null;
let speed=1;
let maskFuture=false;       // true=仅显示已播放；false=显示全部
let lastHoverSec=null;      // 光标秒
let liveTimer=null;         // 增量拉取计时器
let hasFitted=false;        // 是否已自动对齐
chartMain.subscribeCrosshairMove(p=>{ if(p?.time) lastHoverSec=Number(p.time); });

/* ====== 速度 ====== */
function setSpeed(v){ const val=Math.max(0.2,Math.min(120, Number(v)||1));
  speed=val; speedRange.value=String(val); speedInput.value=String(val);
  if(timer){ pause(); play(); }
}
speedRange.oninput=e=>setSpeed(e.target.value);
speedInput.oninput=e=>setSpeed(e.target.value);

/* ====== 聚合到选择周期 ====== */
function aggregate(tfm){
  const mins = maskFuture ? minutesFull.slice(0, Math.min(idxMin+1, minutesFull.length))
                          : minutesFull.slice();
  if(!mins.length) return {agg:[]};
  const bucket=tfm*60,out=[]; let cur=null,b=null;
  for(const m of mins){
    const nb=Math.floor(m.time/bucket);
    if(b===null||nb!==b){b=nb;cur={time:nb*bucket,open:m.open,high:m.high,low:m.low,close:m.close,volume:m.volume??0};out.push(cur);}
    else{cur.high=Math.max(cur.high,m.high);cur.low=Math.min(cur.low,m.low);cur.close=m.close;cur.volume=(cur.volume??0)+(m.volume??0);}
  }
  return {agg:out};
}

/* ====== 渲染（含保底视窗+安全设置） ====== */
function renderAll(){
  const tf = TF_MINUTES[selBar.value] || 1;
  const { agg } = aggregate(tf);

  // 指标只用“已收盘”的K
  let aggForInd = agg;
  if (maskFuture && agg.length && minutesFull.length) {
    const tfSec = (TF_MINUTES[selBar.value] || 1) * 60;
    const lastPlaySec  = minutesFull[Math.min(idxMin, minutesFull.length-1)].time;
    const lastAggStart = agg[agg.length - 1].time;
    const lastAggClose = lastAggStart + tfSec;
    if ((lastPlaySec + 60) < lastAggClose) aggForInd = agg.slice(0, -1);
  }

  // 主K
  const candleData = agg
    .filter(d => Number.isFinite(d.time) && [d.open,d.high,d.low,d.close].every(Number.isFinite))
    .map(d => ({ time:d.time, open:d.open, high:d.high, low:d.low, close:d.close }));
  seriesCandle.setData(candleData);
  console.log('candles ok / dropped =', candleData.length, '/', agg.length - candleData.length);

 // —— 保底：第一次渲染时把视窗放到最后 300 根，之后不再改动，完全由你自己缩放
try{
  if (!hasFitted && candleData.length){
    const n = candleData.length;
    const right = candleData[n-1].time;
    const left  = candleData[Math.max(0, n-300)].time;
    safeSetRange(chartMain, left, right);
    hasFitted = true;  // 只做这一次
  }
}catch(_){}

  // SMA / BOLL（只喂有效数字）
  const close = aggForInd.map(x=>x.close),
        high  = aggForInd.map(x=>x.high),
        low   = aggForInd.map(x=>x.low);

  const m5  = SMA(close,5),
        m10 = SMA(close,10),
        m20 = SMA(close,20);

  ma5 .setData(aggForInd.map((d,i)=> Number.isFinite(m5[i])  ? ({ time:d.time, value:m5[i]  }) : null).filter(Boolean));
  ma10.setData(aggForInd.map((d,i)=> Number.isFinite(m10[i]) ? ({ time:d.time, value:m10[i] }) : null).filter(Boolean));
  ma20.setData(aggForInd.map((d,i)=> Number.isFinite(m20[i]) ? ({ time:d.time, value:m20[i] }) : null).filter(Boolean));

  const std20 = STD(close,20) || [];
  const up = m20.map((m,i)=> (Number.isFinite(m) && Number.isFinite(std20[i])) ? (m + 2*std20[i]) : NaN);
  const dn = m20.map((m,i)=> (Number.isFinite(m) && Number.isFinite(std20[i])) ? (m - 2*std20[i]) : NaN);

  bollMid.setData(aggForInd.map((d,i)=> Number.isFinite(m20[i]) ? ({ time:d.time, value:m20[i] }) : null).filter(Boolean));
  bollUp .setData(aggForInd.map((d,i)=> Number.isFinite(up[i])  ? ({ time:d.time, value:up[i]  }) : null).filter(Boolean));
  bollDn .setData(aggForInd.map((d,i)=> Number.isFinite(dn[i])  ? ({ time:d.time, value:dn[i]  }) : null).filter(Boolean));

  // 成交量
  const volData = agg
    .filter(x => typeof x.volume === 'number' && !Number.isNaN(x.volume))
    .map(d => ({ time:d.time, value:d.volume, color: d.close>=d.open ? 'rgba(0,200,0,0.7)' : 'rgba(220,0,0,0.7)'}));
  volSeries.setData(volData);

  // MACD
  if(close.length){
    const e12=EMA(close,12), e26=EMA(close,26);
    const macd = close.map((_,i)=> (e12[i]!=null && e26[i]!=null) ? e12[i]-e26[i] : NaN);
    const sig  = EMA(macd,9) || [];
    const hist = macd.map((x,i)=> (Number.isFinite(x) && Number.isFinite(sig[i])) ? x - sig[i] : NaN);

    const macdData=[], sigData=[], histData=[];
    for(let i=0;i<aggForInd.length;i++){
      const t=aggForInd[i].time;
      if (Number.isFinite(macd[i])) macdData.push({time:t,value:macd[i]});
      if (Number.isFinite(sig[i]))  sigData .push({time:t,value:sig[i]});
      if (Number.isFinite(hist[i])) histData.push({time:t,value:hist[i], color: hist[i]>=0?'rgba(0,200,0,0.7)':'rgba(220,0,0,0.7)'});
    }
    macdLine.setData(macdData);
    macdSig .setData(sigData);
    macdHist.setData(histData);
  }else{
    macdLine.setData([]); macdSig.setData([]); macdHist.setData([]);
  }

  // KDJ
  const hhv9 = HHV(high,9), llv9 = LLV(low,9);
  const rsv  = close.map((c,i)=>{
    if (high[i]==null || low[i]==null || hhv9[i]==null || llv9[i]==null || hhv9[i]===llv9[i]) return null;
    const v = ((c - llv9[i])/(hhv9[i]-llv9[i]))*100;
    return Number.isFinite(v) ? v : null;
  });
  const K=[], D=[];
  for (let i=0;i<rsv.length;i++){
    const r = rsv[i]==null ? 0 : rsv[i];
    K[i] = (i ? K[i-1]*2/3 : 50) + r/3;
    D[i] = (i ? D[i-1]*2/3 : 50) + K[i]/3;
  }
  const J = K.map((k,i)=> 3*k - 2*(D[i] ?? 0));
  kLine.setData(aggForInd.map((d,i)=> Number.isFinite(K[i]) ? ({time:d.time,value:K[i]}) : null).filter(Boolean));
  dLine.setData(aggForInd.map((d,i)=> Number.isFinite(D[i]) ? ({time:d.time,value:D[i]}) : null).filter(Boolean));
  jLine.setData(aggForInd.map((d,i)=> Number.isFinite(J[i]) ? ({time:d.time,value:J[i]}) : null).filter(Boolean));

  // —— 标记已有数据（用于同步可视范围）
  hasDataMain = agg.length > 0;
  hasDataVol  = volData.length > 0;
  hasDataMacd = close.length > 0;
  hasDataKdj  = close.length > 0;

  // —— 仅首次渲染时自适应
  if (hasDataMain && !hasFitted) {
    //try{ chartMain.timeScale().fitContent(); }catch(_){}
    hasFitted = true;
  }
}

/* ====== 播放：结束后保留最后画面 ====== */
function tick(){
  if(idxMin>=minutesFull.length){
    idxMin = Math.max(0, minutesFull.length - 1);
    renderAll();
    pause();
    statusEl.textContent='状态：播放完毕（画面已保留）';
    return;
  }

  renderAll();

  // 播放时（仅勾选锁定时）把窗口固定在最后 600 根内
  try{
    if (maskFuture && lockViewChk.checked){
      const tfm = TF_MINUTES[selBar.value] || 1;
      const { agg } = aggregate(tfm);
      const right = agg[agg.length-1]?.time;
      const left  = agg[Math.max(0, agg.length-600)]?.time;
      safeSetRange(chartMain, left, right);
      chartMain.timeScale().scrollToPosition(0, false);
    }
  }catch(_){}

  idxMin++;
}
function play(){
  if(!minutesFull.length||timer) return;
  if (!maskFuture) {
  maskFuture = true;        // 切到“仅显示播放段”
  // 不再强制把 idxMin 归零，也不再自适应
  hasFitted = false;
  renderAll();
}
  //if(!maskFuture){ maskFuture=true; idxMin=0; hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } }
  statusEl.textContent='状态：播放中…';
  const gap=Math.max(5,Math.floor(1000/speed));
  timer=setInterval(tick,gap);
  if(syncToggle.checked) bc('play',{});
}
function pause(){
  if(timer){clearInterval(timer);timer=null;}
  statusEl.textContent='状态：已暂停';
  if(syncToggle.checked) bc('pause',{});
}
function reset(){
  //pause(); idxMin=0; maskFuture=false; hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){}
  statusEl.textContent='状态：已重置';
  if(syncToggle.checked) bc('reset',{});
}

/* ====== 显示全部 / 仅显示播放段 ====== */
//btnShowPlayed.onclick=()=>{ maskFuture=true;  hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } };
//btnShowAll.onclick   =()=>{ maskFuture=false; hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } };

/* ====== 从光标开始 ====== */
async function startFromCursor(){
  if(!minutesFull.length||!lastHoverSec) return;
  const tfSec = (TF_MINUTES[selBar.value] || 1) * 60;
  const tsAligned = Math.floor(lastHoverSec / tfSec) * tfSec;

  const first=minutesFull[0]?.time||0;
  if(tsAligned<first){ await loadMinutes(tsAligned); }

  let p=minutesFull.findIndex(m=>m.time>=tsAligned); if(p<0)p=minutesFull.length-1;
  idxMin=Math.max(0,p); maskFuture=true; hasFitted=false;

  //renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ }
  play();

  if(syncToggle.checked) bc('jumpTo',{ts:tsAligned,fromCursor:1});
}

/* ====== 取数：加载完先显示整段K线 ====== */
async function loadMinutes(ensureFromTs=null){
  try{
    pause(); statusEl.textContent='状态：取分钟数据中…';
    const nowMs=Date.now(); let count=Number(minutesInput.value||0); let fromMs=null;
    if (ensureFromTs) {
      const aligned = alignTsToFrame(ensureFromTs);
      count = 0; fromMs = aligned * 1000;
    }
    if (!count && jumpTime.value && !ensureFromTs) {
      const s=new Date(jumpTime.value).getTime();
      if(isFinite(s)&&s<nowMs) count=Math.ceil((nowMs-s)/(60*1000))+720;
    }
    const qs=new URLSearchParams({symbol:selSymbol.value});
    if(count) qs.set('count',String(count)); if(fromMs) qs.set('from',String(fromMs));
    const r=await fetch(`/api/minutes?${qs.toString()}`);
    if(!r.ok){statusEl.textContent=`状态：接口错误 ${r.status}`;return;}
    const raw=await r.json(); if(!Array.isArray(raw)||!raw.length){statusEl.textContent='状态：无分钟数据';return;}

    minutesFull=raw.map(x=>({time:Math.floor(x.time/1000),open:x.open,high:x.high,low:x.low,close:x.close,volume:x.volume}));

    // 加载完成：先显示完整K线（不必点开始）
    maskFuture=false; idxMin = minutesFull.length - 1; hasFitted=false;
    //renderAll(); try{ chartMain.timeScale().fitContent(); }catch(_){ }
    statusEl.textContent=`状态：就绪（分钟K ${minutesFull.length} 根）`;

    // 开启增量刷新
    if(liveTimer) clearInterval(liveTimer);
    liveTimer=setInterval(fetchNew, 20000);
  }catch(e){console.error(e);statusEl.textContent='状态：取数失败（看控制台）';}
}

/* ====== 增量拉取（显示全部时自动刷新画面） ====== */
async function fetchNew(){
  if(!minutesFull.length) return;
  try{
    const last=minutesFull[minutesFull.length-1].time*1000;
    const qs=new URLSearchParams({symbol:selSymbol.value,from:String(last+1000)});
    const r=await fetch(`/api/minutes?${qs.toString()}`);
    if(!r.ok) return;
    const raw=await r.json();
    if(Array.isArray(raw)&&raw.length){
      const add=raw.map(x=>({time:Math.floor(x.time/1000),open:x.open,high:x.high,low:x.low,close:x.close,volume:x.volume}));
      const lastT=minutesFull.length?minutesFull[minutesFull.length-1].time:0;
      const merged=add.filter(x=>x.time>lastT);
      if(merged.length){
        minutesFull.push(...merged);
        //if(!maskFuture){ hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } }
      }
    }
  }catch(e){}
}

/* ====== 多页同步（仅播放控制） ====== */
const chan=new BroadcastChannel('kline-sync-v1'); const pageId=Math.random().toString(36).slice(2);
function bc(cmd,payload){ chan.postMessage({pageId,cmd,payload}); }
chan.onmessage=e=>{
  const {pageId:from,cmd,payload}=e.data||{}; if(from===pageId||!syncToggle.checked) return;
  if(cmd==='play') play(); else if(cmd==='pause') pause(); else if(cmd==='reset') reset();
  else if(cmd==='reload') loadMinutes();
  else if(cmd==='jumpTo'){
    const ts=payload.ts;
    if(payload.fromCursor){ startFromCursor(); }
    else{
      let p=minutesFull.findIndex(m=>m.time>=ts); if(p<0)p=minutesFull.length-1;
      //idxMin=Math.max(0,p); maskFuture=true; hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ }
      play();
    }
  }
};

/* ====== 事件绑定 ====== */
document.getElementById('startBtn').onclick=play;
document.getElementById('pauseBtn').onclick=pause;
document.getElementById('resetBtn').onclick=reset;
document.getElementById('reloadBtn').onclick=()=>{ loadMinutes(); if(syncToggle.checked) bc('reload',{}); };

// 跳转并播放：无论如何先从后端按 from=ts 取数，确保“从你设定的时间”开始
document.getElementById('jumpBtn').onclick=async ()=>{
  if(!jumpTime.value) return;
  const raw = Math.floor(new Date(jumpTime.value).getTime()/1000);
  const ts  = alignTsToFrame(raw);
  await loadMinutes(ts);
  let p=minutesFull.findIndex(m=>m.time>=ts); if(p<0)p=minutesFull.length-1;
  idxMin=Math.max(0,p); maskFuture=true; hasFitted=false;
  //renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ }
  play();
  if(syncToggle.checked) bc('jumpTo',{ts});
};

startFromCursorBtn.onclick=startFromCursor;
selSymbol.onchange =()=>{ loadMinutes(); };
//selBar.onchange    =()=>{ hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } };

/* ====== URL 预设（可选） ====== */
(function applyQuery(){
  const q=new URLSearchParams(location.search);
  if(q.get('symbol')) selSymbol.value=q.get('symbol');
  if(q.get('bar'))    selBar.value=q.get('bar');
  if(q.get('speed'))  setSpeed(parseFloat(q.get('speed')));
  if(q.get('count'))  minutesInput.value=q.get('count');
  if(q.get('sync')==='1') syncToggle.checked=true;
  if(q.get('start'))  jumpTime.value=q.get('start').slice(0,16);
})();

/* ====== 右键菜单 ====== */
const ctx=document.getElementById('ctxMenu');
mainWrap.addEventListener('contextmenu',e=>{
  e.preventDefault();
  if(!lastHoverSec){ctx.style.display='none';return;}
  ctx.style.left=e.clientX+'px'; ctx.style.top=e.clientY+'px'; ctx.style.display='block';
});
window.addEventListener('click',()=>ctx.style.display='none');
document.getElementById('ctxStartHere').onclick=()=>{ ctx.style.display='none'; startFromCursor(); };
//document.getElementById('ctxShowAll').onclick =()=>{ ctx.style.display='none'; maskFuture=false; hasFitted=false; renderAll(); try{chartMain.timeScale().fitContent();}catch(_){ } };

// === 安全可视窗口（播放时把“镜头”锁在最后 600 根内，并轻柔追尾）===
(function(){
  const SAFE_VIEW_SIZE = 600;
  const SAFE_VIEW_MS   = 300;
  function keepSafeView(){
  try{
    // 仅在“仅显示播放段”+“锁定窗口”时，让视图跟在最右边
    if (!maskFuture || !lockViewChk.checked || !chartMain) return;
    chartMain.timeScale().scrollToPosition(0, false); // 只移动，不改缩放
  }catch(e){}
}

  setInterval(keepSafeView, SAFE_VIEW_MS);
})();

/* ====== 首次取数（加载完即显示整段） ====== */
loadMinutes();
</script>

<script>
/* 打开页面后自动尝试加载缓存（有就用，没有就跳过，不影响正常取数） */
(async function autoLoadCache(){
  try {
    const sym = document.getElementById('symbolSel').value;
    const r = await fetch(`/api/cache?symbol=${encodeURIComponent(sym)}`);
    if (r.ok && r.status !== 204) {
      const arr = await r.json();
      if (Array.isArray(arr) && arr.length){
        minutesFull = arr;
        idxMin = 0; maskFuture = false; hasFitted = false;
        renderAll();
        //try{ const lockViewChk = document.getElementById('lockViewChk'); if (!lockViewChk || !lockViewChk.checked) chartMain.timeScale().fitContent(); }catch(_){}
        setStatus(`就绪（缓存 ${arr.length} 条 1分钟K）`);
      }
    }
  } catch(_){}
})();
</script>

</body>
</html>
