// server.js —— 稳定分页：第1页用 candles，后续用 history-candles
const express = require('express');
const axios = require('axios');
const fs   = require('fs');
const path = require('path');
const CACHE_DIR = path.join(__dirname, 'cache');
if (!fs.existsSync(CACHE_DIR)) fs.mkdirSync(CACHE_DIR);
const cacheFile = (sym) => path.join(CACHE_DIR, `${sym}.minutes.json`);
const { HttpsProxyAgent } = require('https-proxy-agent');


const app = express();
const PORT = 3000;

app.use(express.static(path.join(__dirname, 'public')));

// 你的本地代理（你之前提供的是 7897）
const proxyAgent = new HttpsProxyAgent('http://127.0.0.1:7897');

// -------- 旧接口：一次性转发（保留） --------
app.get('/api/kline', async (req, res) => {
  const symbol = req.query.symbol || 'BTC-USDT-SWAP';
  const bar    = req.query.bar    || '1m';
  const limit  = Number(req.query.limit || 500);
  const url = 'https://www.okx.com/api/v5/market/candles';
  console.log(`➡ /api/kline ${symbol} ${bar} limit=${limit}`);
  try {
    const r = await axios.get(url, {
      params: { instId: symbol, bar, limit },
      httpsAgent: proxyAgent,
      timeout: 30000,
    });
    const rows = Array.isArray(r.data?.data) ? r.data.data : [];
    const list = rows.map(x => ({
      time:  Number(x[0]),
      open:  Number(x[1]),
      high:  Number(x[2]),
      low:   Number(x[3]),
      close: Number(x[4]),
      volume:Number(x[5] ?? 0),
    })).reverse();
    console.log(`✅ /api/kline ok -> ${list.length}`);
    res.json(list);
  } catch (e) {
    console.error('❌ /api/kline', e.message);
    res.status(500).json({ error: 'fetch-failed', message: e.message });
  }
});

// -------- 新接口：分钟无限分页（稳定版） --------
/**
 * /api/minutes?symbol=BTC-USDT-SWAP&count=1440&from=ms_optional
 * - count：想要的分钟数；不传则用内部安全上限（约14天）
 * - from：起点毫秒时间戳（可选），会一直往前翻直到覆盖到该时间
 * 逻辑：
 *   第1页用  /market/candles  拿最新
 *   后续用   /market/history-candles  + before 翻旧数据
 */
app.get('/api/minutes', async (req, res) => {
  const symbol = req.query.symbol || 'BTC-USDT-SWAP';
  const want   = Number(req.query.count || 0);       // 0 = 不指定数量
  const fromMs = req.query.from ? Number(req.query.from) : null;

  const MAX_CHUNK = 300;     // OKX 单页最大
  const MAX_SAFE  = 20000;   // 内部安全上限（约14天），需要更大再调

  console.log(`➡ /api/minutes ${symbol} want=${want||'auto'} from=${fromMs||'-'}`);

  const apiCandles = 'https://www.okx.com/api/v5/market/candles';
  const apiHistory = 'https://www.okx.com/api/v5/market/history-candles';

  let remain = want > 0 ? want : MAX_SAFE;
  let before = undefined;      // 毫秒时间戳，用于 history 翻页
  const all = [];

  try {
    // 第1页：用 candles 拿最新
    let batch = Math.min(MAX_CHUNK, remain);
    let r1 = await axios.get(apiCandles, {
      params: { instId: symbol, bar: '1m', limit: batch },
      httpsAgent: proxyAgent,
      timeout: 30000,
    });
    let rows = Array.isArray(r1.data?.data) ? r1.data.data : [];
    if (rows.length) {
      const mapped = rows.map(x => ({
        time:  Number(x[0]),
        open:  Number(x[1]),
        high:  Number(x[2]),
        low:   Number(x[3]),
        close: Number(x[4]),
        volume:Number(x[5] ?? 0),
      }));
      all.push(...mapped);
      const oldest = mapped[mapped.length - 1].time;
      before = oldest - 1;
      remain -= rows.length;
    }

    // 后续页：用 history-candles + before 向更早翻
    while (remain > 0 && before) {
      batch = Math.min(MAX_CHUNK, remain);
      const r = await axios.get(apiHistory, {
        params: { instId: symbol, bar: '1m', limit: batch, before },
        httpsAgent: proxyAgent,
        timeout: 30000,
      });
      rows = Array.isArray(r.data?.data) ? r.data.data : [];
      if (!rows.length) break;

      const mapped = rows.map(x => ({
        time:  Number(x[0]),
        open:  Number(x[1]),
        high:  Number(x[2]),
        low:   Number(x[3]),
        close: Number(x[4]),
        volume:Number(x[5] ?? 0),
      }));

      all.push(...mapped);
      const oldest = mapped[mapped.length - 1].time;
      before = oldest - 1;
      remain -= rows.length;

      if (fromMs && oldest <= fromMs) break; // 覆盖到起点即可停
    }

    // 统一从旧到新
    let out = all.reverse();
    if (fromMs) out = out.filter(x => x.time >= fromMs);
    if (want > 0 && out.length > want) out = out.slice(-want);

    console.log(`✅ /api/minutes ok -> ${out.length}`);
    res.json(out);
  } catch (e) {
    console.error('❌ /api/minutes', e.message);
    res.status(500).json({ error: 'fetch-failed', message: e.message });
  }
});

// 读取本地缓存
app.get('/api/cache', (req, res) => {
  const sym = (req.query.symbol || 'BTC-USDT-SWAP').trim();
  const file = cacheFile(sym);
  if (!fs.existsSync(file)) return res.status(204).end(); // 没缓存
  try {
    const json = JSON.parse(fs.readFileSync(file, 'utf8'));
    res.json(json);
  } catch (e) {
    res.status(500).json({ error: String(e) });
  }
});

// 覆盖保存本地缓存
app.post('/api/cache', express.json({ limit: '100mb' }), (req, res) => {
  const sym = (req.body.symbol || 'BTC-USDT-SWAP').trim();
  const arr = Array.isArray(req.body.minutes) ? req.body.minutes : [];
  const file = cacheFile(sym);
  fs.writeFileSync(file, JSON.stringify(arr));
  res.json({ ok: true, count: arr.length });
});


app.listen(PORT, () => {
  console.log(`✅ 本地服务器已启动: http://localhost:${PORT}`);
});
