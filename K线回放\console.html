<!DOCTYPE html><html lang="zh"><head><meta charset="UTF-8"><title>播放控制台（仅播放控制）</title>
  <style>body{background:#0b0b0b;color:#eee;font-family:system-ui;margin:0;padding:16px}
  button{background:#222;color:#fff;border:1px solid #444;border-radius:6px;padding:8px 14px;cursor:pointer;margin-right:8px}button:hover{background:#333}</style>
  </head><body>
  <button id="play">▶ 开始</button><button id="pause">⏸ 暂停</button><button id="reset">🔄 重置</button><button id="reload">↻ 重新取数</button>
  <script>
  const ch=new BroadcastChannel('kline-sync-v1');const id='console';
  const send=(c,p)=>ch.postMessage({pageId:id,cmd:c,payload:p});
  play.onclick=()=>send('play',{}); pause.onclick=()=>send('pause',{}); reset.onclick=()=>send('reset',{}); reload.onclick=()=>send('reload',{});
  </script></body></html>
  